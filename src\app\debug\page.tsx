'use client';

import { useState, useEffect } from 'react';
import { loadSegmentsFromStorage, saveSegmentsToStorage, clearStoredSegments } from '@/utils/textUtils';
import { TextSegment } from '@/types';

export default function DebugPage() {
  const [storedData, setStoredData] = useState<TextSegment[]>([]);
  const [rawData, setRawData] = useState<string>('');

  const loadData = () => {
    const segments = loadSegmentsFromStorage();
    setStoredData(segments);
    
    // 获取原始存储数据
    const raw = localStorage.getItem('podcast_segments');
    setRawData(raw || '无数据');
  };

  const testSave = () => {
    const testData: TextSegment[] = [
      { role: 'Cherry', text: '这是测试文本1' },
      { role: 'Serena', text: '这是测试文本2' }
    ];
    
    try {
      saveSegmentsToStorage(testData);
      alert('测试数据保存成功');
      loadData();
    } catch (error) {
      alert('保存失败: ' + (error as Error).message);
    }
  };

  const clearData = () => {
    clearStoredSegments();
    loadData();
    alert('数据已清除');
  };

  useEffect(() => {
    loadData();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          本地存储调试页面
        </h1>

        <div className="space-y-6">
          {/* 操作按钮 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              操作
            </h2>
            <div className="space-x-4">
              <button
                onClick={loadData}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                重新加载数据
              </button>
              <button
                onClick={testSave}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                保存测试数据
              </button>
              <button
                onClick={clearData}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                清除所有数据
              </button>
            </div>
          </div>

          {/* 解析后的数据 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              解析后的数据 ({storedData.length} 条)
            </h2>
            {storedData.length > 0 ? (
              <div className="space-y-2">
                {storedData.map((segment, index) => (
                  <div key={index} className="p-3 bg-gray-100 dark:bg-gray-700 rounded">
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      段落 {index + 1} - 角色: {segment.role}
                    </div>
                    <div className="text-gray-900 dark:text-white">
                      {segment.text}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 dark:text-gray-400">没有数据</p>
            )}
          </div>

          {/* 原始数据 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              原始存储数据
            </h2>
            <pre className="bg-gray-100 dark:bg-gray-700 p-4 rounded text-sm overflow-auto">
              {rawData}
            </pre>
          </div>

          {/* 浏览器信息 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              浏览器信息
            </h2>
            <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <div>User Agent: {navigator.userAgent}</div>
              <div>本地存储支持: {typeof Storage !== 'undefined' ? '是' : '否'}</div>
              <div>当前域名: {window.location.hostname}</div>
              <div>当前协议: {window.location.protocol}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

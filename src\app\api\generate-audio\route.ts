import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

export async function POST(request: NextRequest) {
  try {
    const { text, voice, index } = await request.json();

    if (!text || !voice || index === undefined) {
      return NextResponse.json(
        { error: '缺少必需参数：text, voice, index' },
        { status: 400 }
      );
    }

    const apiKey = process.env.DASHSCOPE_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: '未配置 DASHSCOPE_API_KEY 环境变量' },
        { status: 500 }
      );
    }

    // 调用阿里云 DashScope API
    const response = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'qwen-tts-latest',
        input: {
          text: text,
          voice: voice
        }
      })
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('DashScope API 错误:', errorData);
      return NextResponse.json(
        { error: `API 调用失败: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    
    if (!data.output?.audio?.url) {
      return NextResponse.json(
        { error: 'API 响应中没有音频 URL' },
        { status: 500 }
      );
    }

    const audioUrl = data.output.audio.url;

    // 下载音频文件
    const audioResponse = await fetch(audioUrl);
    if (!audioResponse.ok) {
      return NextResponse.json(
        { error: '下载音频文件失败' },
        { status: 500 }
      );
    }

    const audioBuffer = await audioResponse.arrayBuffer();
    
    // 确保 public/audio 目录存在
    const audioDir = join(process.cwd(), 'public', 'audio');
    if (!existsSync(audioDir)) {
      await mkdir(audioDir, { recursive: true });
    }

    // 保存音频文件
    const filename = `audio_${index}.mp3`;
    const filepath = join(audioDir, filename);
    await writeFile(filepath, Buffer.from(audioBuffer));

    return NextResponse.json({
      success: true,
      audioUrl: `/audio/${filename}`,
      filename: filename,
      index: index
    });

  } catch (error) {
    console.error('音频生成错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误: ' + (error as Error).message },
      { status: 500 }
    );
  }
}

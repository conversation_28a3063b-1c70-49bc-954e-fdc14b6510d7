# AI 音频播客生成工具

一个基于 Next.js 的 AI 音频播客生成工具，可以将文本转换为专业的多角色音频播客。支持自动分段、角色分配和高质量语音合成。

## 功能特性

- 📝 **文本标注**：自动分段并为每个段落分配角色
- 🎙️ **音频生成**：使用阿里云 DashScope API 生成高质量音频
- 🎵 **播放预览**：支持单独播放和连续播放功能
- 🎭 **多角色支持**：支持旁白、角色1、角色2、采访者、嘉宾等多种角色
- 💾 **本地存储**：自动保存标注数据，支持断点续传
- 🌙 **深色模式**：支持明暗主题切换

## 技术栈

- **前端框架**：Next.js 15 (App Router)
- **样式**：Tailwind CSS 4
- **语言**：TypeScript
- **音频API**：阿里云 DashScope
- **存储**：浏览器本地存储

## 快速开始

### 1. 环境准备

确保您的系统已安装：
- Node.js 18+
- npm 或 yarn

### 2. 安装依赖

```bash
npm install
# 或
yarn install
```

### 3. 环境配置

复制 `.env.local` 文件并配置您的阿里云 DashScope API 密钥：

```bash
# .env.local
DASHSCOPE_API_KEY=your_dashscope_api_key_here
```

> 💡 如何获取 DashScope API 密钥：
> 1. 访问 [阿里云 DashScope 控制台](https://dashscope.console.aliyun.com/)
> 2. 注册/登录阿里云账号
> 3. 开通 DashScope 服务
> 4. 在 API-KEY 管理页面创建新的 API 密钥

### 4. 启动开发服务器

```bash
npm run dev
# 或
yarn dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

## 使用指南

### 步骤 1：文本标注 (`/annotate`)

1. 在文本输入框中粘贴或输入您要转换的文本
2. 点击"自动分段"按钮，系统会按换行符或句号自动分割文本
3. 为每个段落选择合适的角色（旁白、角色1、角色2等）
4. 点击"保存"按钮保存标注结果
5. 点击"下一步：生成音频"进入音频生成页面

### 步骤 2：音频生成 (`/generate`)

1. 系统会自动加载已保存的文本标注数据
2. 点击"开始生成音频"按钮开始批量生成
3. 系统会按顺序为每个段落调用 AI 语音合成 API
4. 生成的音频文件会自动保存到 `public/audio/` 目录
5. 可以实时查看生成进度和播放已完成的音频
6. 如果某段音频生成失败，可以点击"重试"按钮

### 步骤 3：播放预览 (`/preview`)

1. 查看所有文本段落和对应的音频播放器
2. 可以单独播放每段音频，或点击"播放全部"连续播放
3. 支持暂停、重新播放等控制功能
4. 可以返回前面的步骤重新编辑或生成

## 项目结构

```
src/
├── app/                    # Next.js App Router 页面
│   ├── annotate/          # 文本标注页面
│   ├── generate/          # 音频生成页面
│   ├── preview/           # 播放预览页面
│   └── api/               # API 路由
├── components/            # 可复用组件
├── types/                 # TypeScript 类型定义
└── utils/                 # 工具函数
    ├── textUtils.ts       # 文本处理和存储
    └── dataValidation.ts  # 数据验证
```

## API 接口

### POST `/api/generate-audio`

生成单个音频文件的 API 接口。

**请求参数：**
```json
{
  "text": "要转换的文本内容",
  "voice": "角色名称（narrator/character1/character2/interviewer/guest）",
  "index": 0
}
```

**响应：**
```json
{
  "success": true,
  "audioUrl": "/audio/audio_0.mp3",
  "filename": "audio_0.mp3",
  "index": 0
}
```

## 数据格式

### 文本段落数据结构

```json
[
  {
    "role": "narrator",
    "text": "这是第一段内容..."
  },
  {
    "role": "character1",
    "text": "这是第二段内容..."
  }
]
```

### 支持的角色类型

- `narrator` - 旁白
- `character1` - 角色1
- `character2` - 角色2
- `interviewer` - 采访者
- `guest` - 嘉宾

## 故障排除

### 常见问题

1. **API 密钥错误**
   - 检查 `.env.local` 文件中的 `DASHSCOPE_API_KEY` 是否正确
   - 确认 API 密钥有效且有足够的调用额度

2. **音频生成失败**
   - 检查网络连接
   - 确认文本内容不包含特殊字符
   - 检查 API 调用频率是否过高

3. **音频文件无法播放**
   - 确认 `public/audio/` 目录存在且有写入权限
   - 检查浏览器是否支持 MP3 格式

4. **页面加载缓慢**
   - 清除浏览器缓存
   - 检查本地存储数据是否过大

### 开发调试

启用详细日志：
```bash
# 开发模式下查看详细错误信息
npm run dev
```

查看浏览器控制台获取详细错误信息。

## 部署

### Vercel 部署（推荐）

1. 将代码推送到 GitHub 仓库
2. 在 [Vercel](https://vercel.com) 中导入项目
3. 在环境变量中配置 `DASHSCOPE_API_KEY`
4. 部署完成

### 其他平台

确保部署平台支持：
- Node.js 18+
- 文件系统写入权限（用于保存音频文件）
- 环境变量配置

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v1.0.0
- 初始版本发布
- 支持文本标注、音频生成、播放预览功能
- 集成阿里云 DashScope API
- 支持多角色语音合成

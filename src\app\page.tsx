import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Mic, FileText, Play, ArrowRight, Sparkles, Users, Zap } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted/20 to-background">
      <div className="container mx-auto px-4 py-16">
        {/* Hero Section */}
        <div className="text-center mb-20">
          <div className="flex justify-center mb-6">
            <Badge variant="secondary" className="px-4 py-2 text-sm">
              <Sparkles className="w-4 h-4 mr-2" />
              AI 驱动的音频生成
            </Badge>
          </div>
          <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent mb-6">
            AI 音频播客生成工具
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            将您的文本转换为专业的多角色音频播客。支持自动分段、角色分配和高质量语音合成。
          </p>
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            <Badge variant="outline" className="flex items-center gap-2">
              <Users className="w-3 h-3" />
              多角色对话
            </Badge>
            <Badge variant="outline" className="flex items-center gap-2">
              <Zap className="w-3 h-3" />
              智能分段
            </Badge>
            <Badge variant="outline" className="flex items-center gap-2">
              <Mic className="w-3 h-3" />
              高质量语音
            </Badge>
          </div>
        </div>

        {/* Feature Cards */}
        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto mb-20">
          {/* 文本标注模块 */}
          <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-2 hover:border-primary/20">
            <CardHeader className="text-center pb-4">
              <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                <FileText className="w-8 h-8 text-blue-600 dark:text-blue-400" />
              </div>
              <CardTitle className="text-xl">文本标注</CardTitle>
              <CardDescription>
                输入文本，自动分段并为每个段落分配角色
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <Link href="/annotate">
                <Button className="w-full group/btn">
                  开始标注
                  <ArrowRight className="w-4 h-4 ml-2 group-hover/btn:translate-x-1 transition-transform" />
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* 音频生成模块 */}
          <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-2 hover:border-primary/20">
            <CardHeader className="text-center pb-4">
              <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                <Mic className="w-8 h-8 text-green-600 dark:text-green-400" />
              </div>
              <CardTitle className="text-xl">音频生成</CardTitle>
              <CardDescription>
                使用 AI 技术生成高质量的多角色音频
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <Link href="/generate">
                <Button className="w-full group/btn">
                  生成音频
                  <ArrowRight className="w-4 h-4 ml-2 group-hover/btn:translate-x-1 transition-transform" />
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* 播放预览模块 */}
          <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-2 hover:border-primary/20">
            <CardHeader className="text-center pb-4">
              <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900/20 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                <Play className="w-8 h-8 text-purple-600 dark:text-purple-400" />
              </div>
              <CardTitle className="text-xl">播放预览</CardTitle>
              <CardDescription>
                预览和播放生成的音频播客内容
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <Link href="/preview">
                <Button className="w-full group/btn">
                  播放预览
                  <ArrowRight className="w-4 h-4 ml-2 group-hover/btn:translate-x-1 transition-transform" />
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>

        {/* Workflow Section */}
        <Card className="max-w-4xl mx-auto">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl mb-2">使用流程</CardTitle>
            <CardDescription className="text-base">
              三个简单步骤，轻松创建专业音频播客
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-500 text-white rounded-full flex items-center justify-center text-lg font-bold mx-auto mb-4">
                  1
                </div>
                <h4 className="font-semibold mb-2">文本标注</h4>
                <p className="text-sm text-muted-foreground">
                  输入您的文本内容，系统自动进行智能分段和角色分配
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-green-500 text-white rounded-full flex items-center justify-center text-lg font-bold mx-auto mb-4">
                  2
                </div>
                <h4 className="font-semibold mb-2">音频生成</h4>
                <p className="text-sm text-muted-foreground">
                  使用先进的 AI 技术，为每个角色生成自然流畅的语音
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-500 text-white rounded-full flex items-center justify-center text-lg font-bold mx-auto mb-4">
                  3
                </div>
                <h4 className="font-semibold mb-2">播放预览</h4>
                <p className="text-sm text-muted-foreground">
                  预览完整的音频播客，支持下载和分享
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

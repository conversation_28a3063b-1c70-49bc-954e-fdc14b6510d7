import { TextSegment, VoiceRole, VOICE_ROLES } from '@/types';

/**
 * 验证文本段落数据格式
 * @param data 要验证的数据
 * @returns 验证结果
 */
export function validateSegmentsData(data: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // 检查是否为数组
  if (!Array.isArray(data)) {
    errors.push('数据必须是数组格式');
    return { isValid: false, errors };
  }

  // 检查数组是否为空
  if (data.length === 0) {
    errors.push('数据不能为空');
    return { isValid: false, errors };
  }

  // 验证每个段落
  data.forEach((segment, index) => {
    // 检查必需字段
    if (!segment.hasOwnProperty('role')) {
      errors.push(`段落 ${index + 1} 缺少 role 字段`);
    }
    if (!segment.hasOwnProperty('text')) {
      errors.push(`段落 ${index + 1} 缺少 text 字段`);
    }

    // 检查角色是否有效
    if (segment.role && !VOICE_ROLES.some(role => role.value === segment.role)) {
      errors.push(`段落 ${index + 1} 的角色 "${segment.role}" 无效`);
    }

    // 检查文本是否为空
    if (segment.text && typeof segment.text === 'string' && segment.text.trim() === '') {
      errors.push(`段落 ${index + 1} 的文本内容不能为空`);
    }

    // 检查数据类型
    if (segment.role && typeof segment.role !== 'string') {
      errors.push(`段落 ${index + 1} 的 role 必须是字符串类型`);
    }
    if (segment.text && typeof segment.text !== 'string') {
      errors.push(`段落 ${index + 1} 的 text 必须是字符串类型`);
    }
  });

  return { isValid: errors.length === 0, errors };
}

/**
 * 清理和标准化段落数据
 * @param segments 原始段落数据
 * @returns 清理后的段落数据
 */
export function sanitizeSegments(segments: any[]): TextSegment[] {
  return segments
    .filter(segment => segment && segment.text && segment.text.trim() !== '')
    .map(segment => ({
      role: (VOICE_ROLES.some(role => role.value === segment.role)
        ? segment.role
        : 'Cherry') as VoiceRole,
      text: String(segment.text).trim()
    }));
}

/**
 * 导出段落数据为 JSON 字符串
 * @param segments 段落数据
 * @returns JSON 字符串
 */
export function exportSegmentsToJSON(segments: TextSegment[]): string {
  const validation = validateSegmentsData(segments);
  if (!validation.isValid) {
    throw new Error('数据验证失败：' + validation.errors.join(', '));
  }
  
  return JSON.stringify(segments, null, 2);
}

/**
 * 从 JSON 字符串导入段落数据
 * @param jsonString JSON 字符串
 * @returns 段落数据
 */
export function importSegmentsFromJSON(jsonString: string): TextSegment[] {
  try {
    const data = JSON.parse(jsonString);
    const validation = validateSegmentsData(data);
    
    if (!validation.isValid) {
      throw new Error('数据格式验证失败：' + validation.errors.join(', '));
    }
    
    return sanitizeSegments(data);
  } catch (error) {
    if (error instanceof SyntaxError) {
      throw new Error('JSON 格式错误：' + error.message);
    }
    throw error;
  }
}

/**
 * 获取数据统计信息
 * @param segments 段落数据
 * @returns 统计信息
 */
export function getSegmentsStats(segments: TextSegment[]) {
  const stats = {
    totalSegments: segments.length,
    totalCharacters: segments.reduce((sum, seg) => sum + seg.text.length, 0),
    roleDistribution: {} as Record<VoiceRole, number>
  };

  // 统计角色分布
  VOICE_ROLES.forEach(role => {
    stats.roleDistribution[role.value] = segments.filter(seg => seg.role === role.value).length;
  });

  return stats;
}

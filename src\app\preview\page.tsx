'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { TextSegment, VOICE_ROLES } from '@/types';
import { loadSegmentsFromStorage } from '@/utils/textUtils';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Play, Pause, SkipForward, SkipBack, Download, Volume2, AlertCircle, Loader2 } from "lucide-react";

export default function PreviewPage() {
  const [segments, setSegments] = useState<TextSegment[]>([]);
  const [currentPlayingIndex, setCurrentPlayingIndex] = useState<number | null>(null);
  const [isPlayingAll, setIsPlayingAll] = useState(false);
  const [error, setError] = useState('');
  const [audioFiles, setAudioFiles] = useState<string[]>([]);
  const audioRefs = useRef<(HTMLAudioElement | null)[]>([]);

  // 页面加载时读取数据
  useEffect(() => {
    const savedSegments = loadSegmentsFromStorage();
    if (savedSegments.length === 0) {
      setError('没有找到文本数据，请先完成文本标注');
      return;
    }
    
    setSegments(savedSegments);
    
    // 检查音频文件是否存在
    const audioFileList: string[] = [];
    savedSegments.forEach((_, index) => {
      const audioPath = `/audio/audio_${index}.mp3`;
      audioFileList.push(audioPath);
    });
    setAudioFiles(audioFileList);
    
    // 初始化音频引用数组
    audioRefs.current = new Array(savedSegments.length).fill(null);
  }, []);

  // 获取角色显示名称
  const getRoleLabel = (role: string) => {
    const roleInfo = VOICE_ROLES.find(r => r.value === role);
    return roleInfo ? roleInfo.label : role;
  };

  // 播放单个音频
  const playSingleAudio = (index: number) => {
    const audio = audioRefs.current[index];
    if (!audio) return;

    // 停止其他正在播放的音频
    audioRefs.current.forEach((audioElement, i) => {
      if (audioElement && i !== index) {
        audioElement.pause();
        audioElement.currentTime = 0;
      }
    });

    setCurrentPlayingIndex(index);
    audio.play().catch(error => {
      console.error('播放音频失败:', error);
      setError(`播放第 ${index + 1} 段音频失败`);
    });
  };

  // 暂停单个音频
  const pauseSingleAudio = (index: number) => {
    const audio = audioRefs.current[index];
    if (audio) {
      audio.pause();
      setCurrentPlayingIndex(null);
    }
  };

  // 播放全部音频（按顺序）
  const playAllAudio = async () => {
    if (isPlayingAll) {
      // 停止播放全部
      setIsPlayingAll(false);
      audioRefs.current.forEach(audio => {
        if (audio) {
          audio.pause();
          audio.currentTime = 0;
        }
      });
      setCurrentPlayingIndex(null);
      return;
    }

    setIsPlayingAll(true);
    
    for (let i = 0; i < segments.length; i++) {
      if (!isPlayingAll) break; // 如果用户停止了播放
      
      const audio = audioRefs.current[i];
      if (!audio) continue;

      setCurrentPlayingIndex(i);
      
      try {
        await new Promise<void>((resolve, reject) => {
          const handleEnded = () => {
            audio.removeEventListener('ended', handleEnded);
            audio.removeEventListener('error', handleError);
            resolve();
          };
          
          const handleError = () => {
            audio.removeEventListener('ended', handleEnded);
            audio.removeEventListener('error', handleError);
            reject(new Error(`播放第 ${i + 1} 段音频失败`));
          };

          audio.addEventListener('ended', handleEnded);
          audio.addEventListener('error', handleError);
          
          audio.play().catch(reject);
        });
      } catch (error) {
        console.error('播放错误:', error);
        setError((error as Error).message);
        break;
      }
    }
    
    setIsPlayingAll(false);
    setCurrentPlayingIndex(null);
  };

  // 处理音频播放结束
  const handleAudioEnded = (index: number) => {
    if (currentPlayingIndex === index && !isPlayingAll) {
      setCurrentPlayingIndex(null);
    }
  };

  // 处理音频加载错误
  const handleAudioError = (index: number) => {
    console.error(`音频文件 ${index} 加载失败`);
  };

  if (segments.length === 0 && !error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* 头部导航 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link 
              href="/generate" 
              className="text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300"
            >
              ← 返回生成
            </Link>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              播放预览
            </h1>
          </div>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            {error}
            {error.includes('没有找到文本数据') && (
              <div className="mt-2">
                <Link 
                  href="/annotate" 
                  className="text-red-800 underline hover:text-red-900"
                >
                  前往文本标注页面
                </Link>
              </div>
            )}
          </div>
        )}

        {segments.length > 0 && (
          <>
            {/* 播放控制面板 */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    播客预览
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    共 {segments.length} 段音频，点击播放按钮开始收听
                  </p>
                </div>
                
                <button
                  onClick={playAllAudio}
                  className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                    isPlayingAll
                      ? 'bg-red-600 hover:bg-red-700 text-white'
                      : 'bg-purple-600 hover:bg-purple-700 text-white'
                  }`}
                >
                  {isPlayingAll ? (
                    <div className="flex items-center space-x-2">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      <span>停止播放</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                      </svg>
                      <span>播放全部</span>
                    </div>
                  )}
                </button>
              </div>
            </div>

            {/* 音频段落列表 */}
            <div className="space-y-6">
              {segments.map((segment, index) => (
                <div 
                  key={index} 
                  className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 transition-all duration-300 ${
                    currentPlayingIndex === index ? 'ring-2 ring-purple-500 bg-purple-50 dark:bg-purple-900/20' : ''
                  }`}
                >
                  <div className="flex items-start space-x-4">
                    {/* 播放按钮 */}
                    <button
                      onClick={() => {
                        if (currentPlayingIndex === index) {
                          pauseSingleAudio(index);
                        } else {
                          playSingleAudio(index);
                        }
                      }}
                      className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center transition-colors ${
                        currentPlayingIndex === index
                          ? 'bg-purple-600 hover:bg-purple-700 text-white'
                          : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300'
                      }`}
                    >
                      {currentPlayingIndex === index ? (
                        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      ) : (
                        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                        </svg>
                      )}
                    </button>

                    <div className="flex-1">
                      {/* 段落信息 */}
                      <div className="flex items-center space-x-3 mb-3">
                        <span className="text-lg font-medium text-gray-900 dark:text-white">
                          段落 {index + 1}
                        </span>
                        <span className="px-3 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 rounded-full text-sm font-medium">
                          {getRoleLabel(segment.role)}
                        </span>
                        {currentPlayingIndex === index && (
                          <span className="px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded text-xs animate-pulse">
                            正在播放
                          </span>
                        )}
                      </div>

                      {/* 文本内容 */}
                      <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                        {segment.text}
                      </p>

                      {/* 音频播放器 */}
                      <audio
                        ref={el => audioRefs.current[index] = el}
                        controls
                        className="w-full"
                        onEnded={() => handleAudioEnded(index)}
                        onError={() => handleAudioError(index)}
                        preload="metadata"
                      >
                        <source src={audioFiles[index]} type="audio/mpeg" />
                        您的浏览器不支持音频播放。
                      </audio>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* 底部操作 */}
            <div className="mt-12 text-center space-y-4">
              <div className="flex justify-center space-x-4">
                <Link
                  href="/annotate"
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  重新标注
                </Link>
                <Link
                  href="/generate"
                  className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  重新生成
                </Link>
              </div>
              
              <p className="text-gray-500 dark:text-gray-400 text-sm">
                您的 AI 音频播客已生成完成！
              </p>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

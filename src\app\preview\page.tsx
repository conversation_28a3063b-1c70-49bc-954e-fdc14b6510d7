'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { TextSegment, VOICE_ROLES } from '@/types';
import { loadSegmentsFromStorage } from '@/utils/textUtils';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Play, Pause, SkipForward, SkipBack, Download, Volume2, AlertCircle, Loader2 } from "lucide-react";

export default function PreviewPage() {
  const [segments, setSegments] = useState<TextSegment[]>([]);
  const [currentPlayingIndex, setCurrentPlayingIndex] = useState<number | null>(null);
  const [isPlayingAll, setIsPlayingAll] = useState(false);
  const [error, setError] = useState('');
  const [audioFiles, setAudioFiles] = useState<string[]>([]);
  const audioRefs = useRef<(HTMLAudioElement | null)[]>([]);

  // 页面加载时读取数据
  useEffect(() => {
    const savedSegments = loadSegmentsFromStorage();
    if (savedSegments.length === 0) {
      setError('没有找到文本数据，请先完成文本标注');
      return;
    }
    
    setSegments(savedSegments);
    
    // 检查音频文件是否存在
    const audioFileList: string[] = [];
    savedSegments.forEach((_, index) => {
      const audioPath = `/audio/audio_${index}.mp3`;
      audioFileList.push(audioPath);
    });
    setAudioFiles(audioFileList);
    
    // 初始化音频引用数组
    audioRefs.current = new Array(savedSegments.length).fill(null);
  }, []);

  // 获取角色显示名称
  const getRoleLabel = (role: string) => {
    const roleInfo = VOICE_ROLES.find(r => r.value === role);
    return roleInfo ? roleInfo.label : role;
  };

  // 播放单个音频
  const playSingleAudio = (index: number) => {
    const audio = audioRefs.current[index];
    if (!audio) return;

    // 停止其他正在播放的音频
    audioRefs.current.forEach((audioElement, i) => {
      if (audioElement && i !== index) {
        audioElement.pause();
        audioElement.currentTime = 0;
      }
    });

    setCurrentPlayingIndex(index);
    audio.play().catch(error => {
      console.error('播放音频失败:', error);
      setError(`播放第 ${index + 1} 段音频失败`);
    });
  };

  // 暂停单个音频
  const pauseSingleAudio = (index: number) => {
    const audio = audioRefs.current[index];
    if (audio) {
      audio.pause();
      setCurrentPlayingIndex(null);
    }
  };

  // 播放全部音频（按顺序）
  const playAllAudio = async () => {
    if (isPlayingAll) {
      // 停止播放全部
      setIsPlayingAll(false);
      audioRefs.current.forEach(audio => {
        if (audio) {
          audio.pause();
          audio.currentTime = 0;
        }
      });
      setCurrentPlayingIndex(null);
      return;
    }

    setIsPlayingAll(true);
    
    for (let i = 0; i < segments.length; i++) {
      if (!isPlayingAll) break; // 如果用户停止了播放
      
      const audio = audioRefs.current[i];
      if (!audio) continue;

      setCurrentPlayingIndex(i);
      
      try {
        await new Promise<void>((resolve, reject) => {
          const handleEnded = () => {
            audio.removeEventListener('ended', handleEnded);
            audio.removeEventListener('error', handleError);
            resolve();
          };
          
          const handleError = () => {
            audio.removeEventListener('ended', handleEnded);
            audio.removeEventListener('error', handleError);
            reject(new Error(`播放第 ${i + 1} 段音频失败`));
          };

          audio.addEventListener('ended', handleEnded);
          audio.addEventListener('error', handleError);
          
          audio.play().catch(reject);
        });
      } catch (error) {
        console.error('播放错误:', error);
        setError((error as Error).message);
        break;
      }
    }
    
    setIsPlayingAll(false);
    setCurrentPlayingIndex(null);
  };

  // 处理音频播放结束
  const handleAudioEnded = (index: number) => {
    if (currentPlayingIndex === index && !isPlayingAll) {
      setCurrentPlayingIndex(null);
    }
  };

  // 处理音频加载错误
  const handleAudioError = (index: number) => {
    console.error(`音频文件 ${index} 加载失败`);
  };

  if (segments.length === 0 && !error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted/20 to-background">
      <div className="container mx-auto px-4 py-8">
        {/* 头部导航 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link href="/generate">
              <Button variant="ghost" size="sm" className="gap-2">
                <ArrowLeft className="w-4 h-4" />
                返回生成
              </Button>
            </Link>
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <Volume2 className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">播放预览</h1>
                <p className="text-muted-foreground">预览和播放生成的音频播客</p>
              </div>
            </div>
          </div>
          <Badge variant="outline" className="gap-2">
            <Volume2 className="w-3 h-3" />
            {segments.length} 个音频片段
          </Badge>
        </div>

        {error && (
          <Card className="mb-6 border-destructive">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <AlertCircle className="w-5 h-5 text-destructive mt-0.5" />
                <div>
                  <p className="text-destructive font-medium">{error}</p>
                  {error.includes('没有找到文本数据') && (
                    <div className="mt-2">
                      <Link href="/annotate">
                        <Button variant="outline" size="sm">
                          前往文本标注页面
                        </Button>
                      </Link>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {segments.length > 0 && (
          <>
            {/* 播放控制面板 */}
            <Card className="mb-8">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Volume2 className="w-5 h-5" />
                      播客预览
                    </CardTitle>
                    <CardDescription>
                      共 {segments.length} 段音频，点击播放按钮开始收听完整播客
                    </CardDescription>
                  </div>
                  <Button
                    onClick={playAllAudio}
                    size="lg"
                    variant={isPlayingAll ? "destructive" : "default"}
                    className="gap-2"
                  >
                    {isPlayingAll ? (
                      <>
                        <Pause className="w-5 h-5" />
                        停止播放
                      </>
                    ) : (
                      <>
                        <Play className="w-5 h-5" />
                        播放全部
                      </>
                    )}
                  </Button>
                </div>
              </CardHeader>
            </Card>

            {/* 音频段落列表 */}
            <div className="space-y-6">
              {segments.map((segment, index) => (
                <Card
                  key={index}
                  className={`transition-all duration-300 ${
                    currentPlayingIndex === index ? 'ring-2 ring-primary shadow-lg' : ''
                  }`}
                >
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      {/* 播放按钮 */}
                      <Button
                        onClick={() => {
                          if (currentPlayingIndex === index) {
                            pauseSingleAudio(index);
                          } else {
                            playSingleAudio(index);
                          }
                        }}
                        size="icon"
                        variant={currentPlayingIndex === index ? "default" : "outline"}
                        className="flex-shrink-0 w-12 h-12 rounded-full"
                      >
                        {currentPlayingIndex === index ? (
                          <Pause className="w-6 h-6" />
                        ) : (
                          <Play className="w-6 h-6" />
                        )}
                      </Button>

                      <div className="flex-1">
                        {/* 段落信息 */}
                        <div className="flex items-center gap-3 mb-3">
                          <Badge variant="secondary" className="text-sm">
                            段落 {index + 1}
                          </Badge>
                          <Badge variant="outline" className="text-sm">
                            {getRoleLabel(segment.role)}
                          </Badge>
                          {currentPlayingIndex === index && (
                            <Badge variant="default" className="gap-1 animate-pulse bg-green-600">
                              <Volume2 className="w-3 h-3" />
                              正在播放
                            </Badge>
                          )}
                        </div>

                        {/* 文本内容 */}
                        <p className="text-foreground leading-relaxed mb-4">
                          {segment.text}
                        </p>

                        {/* 音频播放器 */}
                        <audio
                          ref={el => audioRefs.current[index] = el}
                          controls
                          className="w-full"
                          onEnded={() => handleAudioEnded(index)}
                          onError={() => handleAudioError(index)}
                          preload="metadata"
                        >
                          <source src={audioFiles[index]} type="audio/mpeg" />
                          您的浏览器不支持音频播放。
                        </audio>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* 底部操作 */}
            <Card className="mt-12">
              <CardContent className="p-8 text-center">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl font-semibold mb-2">🎉 播客生成完成！</h3>
                    <p className="text-muted-foreground">
                      您的 AI 音频播客已成功生成，可以重新编辑或分享给朋友
                    </p>
                  </div>

                  <div className="flex justify-center gap-4">
                    <Link href="/annotate">
                      <Button variant="outline" className="gap-2">
                        <SkipBack className="w-4 h-4" />
                        重新标注
                      </Button>
                    </Link>
                    <Link href="/generate">
                      <Button variant="outline" className="gap-2">
                        <SkipForward className="w-4 h-4" />
                        重新生成
                      </Button>
                    </Link>
                    <Button variant="default" className="gap-2">
                      <Download className="w-4 h-4" />
                      下载播客
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </div>
  );
}

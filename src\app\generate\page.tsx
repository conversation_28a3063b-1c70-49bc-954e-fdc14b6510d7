'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { TextSegment, AudioGenerationStatus, VOICE_ROLES } from '@/types';
import { loadSegmentsFromStorage } from '@/utils/textUtils';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, ArrowRight, Mic, Play, RotateCcw, CheckCircle, Clock, AlertCircle, Loader2 } from "lucide-react";

export default function GeneratePage() {
  const [segments, setSegments] = useState<TextSegment[]>([]);
  const [generationStatus, setGenerationStatus] = useState<AudioGenerationStatus[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(-1);
  const [error, setError] = useState('');

  // 页面加载时读取数据
  useEffect(() => {
    console.log('正在加载保存的数据...'); // 调试信息
    const savedSegments = loadSegmentsFromStorage();
    console.log('读取到的数据:', savedSegments); // 调试信息

    if (savedSegments.length === 0) {
      console.log('没有找到保存的数据'); // 调试信息
      setError('没有找到文本数据，请先完成文本标注');
      return;
    }

    console.log('成功加载', savedSegments.length, '个文本段落'); // 调试信息
    setSegments(savedSegments);
    // 初始化生成状态
    const initialStatus: AudioGenerationStatus[] = savedSegments.map((_, index) => ({
      index,
      status: 'pending'
    }));
    setGenerationStatus(initialStatus);
  }, []);

  // 生成单个音频
  const generateSingleAudio = async (segment: TextSegment, index: number): Promise<boolean> => {
    try {
      setCurrentIndex(index);
      
      // 更新状态为生成中
      setGenerationStatus(prev => prev.map(status => 
        status.index === index 
          ? { ...status, status: 'generating' }
          : status
      ));

      const response = await fetch('/api/generate-audio', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: segment.text,
          voice: segment.role,
          index: index
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '生成失败');
      }

      // 更新状态为完成
      setGenerationStatus(prev => prev.map(status => 
        status.index === index 
          ? { 
              ...status, 
              status: 'completed',
              audioUrl: data.audioUrl
            }
          : status
      ));

      return true;
    } catch (error) {
      console.error(`生成音频 ${index} 失败:`, error);
      
      // 更新状态为错误
      setGenerationStatus(prev => prev.map(status => 
        status.index === index 
          ? { 
              ...status, 
              status: 'error',
              error: (error as Error).message
            }
          : status
      ));

      return false;
    }
  };

  // 开始生成所有音频
  const startGeneration = async () => {
    if (segments.length === 0) {
      setError('没有可生成的文本段落');
      return;
    }

    setIsGenerating(true);
    setError('');

    try {
      for (let i = 0; i < segments.length; i++) {
        const success = await generateSingleAudio(segments[i], i);
        if (!success) {
          // 如果某个音频生成失败，询问是否继续
          const shouldContinue = confirm(`第 ${i + 1} 段音频生成失败，是否继续生成剩余音频？`);
          if (!shouldContinue) {
            break;
          }
        }
        
        // 添加延迟避免API限制
        if (i < segments.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    } catch (error) {
      setError('生成过程中发生错误：' + (error as Error).message);
    } finally {
      setIsGenerating(false);
      setCurrentIndex(-1);
    }
  };

  // 重新生成单个音频
  const retryGeneration = async (index: number) => {
    if (isGenerating) return;
    
    setIsGenerating(true);
    await generateSingleAudio(segments[index], index);
    setIsGenerating(false);
  };

  // 获取角色显示名称
  const getRoleLabel = (role: string) => {
    const roleInfo = VOICE_ROLES.find(r => r.value === role);
    return roleInfo ? roleInfo.label : role;
  };

  // 计算进度
  const completedCount = generationStatus.filter(s => s.status === 'completed').length;
  const progress = segments.length > 0 ? (completedCount / segments.length) * 100 : 0;

  if (segments.length === 0 && !error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-muted/20 to-background flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="p-8 text-center">
            <Loader2 className="w-12 h-12 mx-auto mb-4 animate-spin text-primary" />
            <p className="text-muted-foreground">加载中...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50">
      <div className="container mx-auto px-4 py-8">
        {/* 头部导航 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link href="/annotate">
              <Button variant="ghost" size="sm" className="gap-2">
                <ArrowLeft className="w-4 h-4" />
                返回标注
              </Button>
            </Link>
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <Mic className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-slate-800">音频生成</h1>
                <p className="text-slate-600">使用 AI 技术生成高质量音频</p>
              </div>
            </div>
          </div>
          <Badge variant="outline" className="gap-2">
            <Clock className="w-3 h-3" />
            {completedCount}/{segments.length} 已完成
          </Badge>
        </div>

        {error && (
          <Card className="mb-6 border-destructive">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <AlertCircle className="w-5 h-5 text-destructive mt-0.5" />
                <div>
                  <p className="text-destructive font-medium">{error}</p>
                  {error.includes('没有找到文本数据') && (
                    <div className="mt-2">
                      <Link href="/annotate">
                        <Button variant="outline" size="sm">
                          前往文本标注页面
                        </Button>
                      </Link>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {segments.length > 0 && (
          <>
            {/* 进度概览 */}
            <Card className="mb-8">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Mic className="w-5 h-5" />
                      生成进度
                    </CardTitle>
                    <CardDescription>
                      AI 正在为每个段落生成高质量音频
                    </CardDescription>
                  </div>
                  <Badge variant="secondary" className="text-lg px-3 py-1">
                    {Math.round(progress)}%
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="w-full bg-muted rounded-full h-3">
                  <div
                    className="bg-primary h-3 rounded-full transition-all duration-300"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>

                <div className="flex justify-center">
                  {!isGenerating ? (
                    <Button
                      onClick={startGeneration}
                      size="lg"
                      className="gap-2"
                      disabled={completedCount === segments.length}
                    >
                      <Play className="w-4 h-4" />
                      {completedCount === segments.length ? '生成完成' : '开始生成音频'}
                    </Button>
                  ) : (
                    <div className="flex items-center gap-3">
                      <Loader2 className="w-6 h-6 animate-spin text-primary" />
                      <span className="text-muted-foreground">
                        正在生成第 {currentIndex + 1} 段音频...
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* 段落列表 */}
            <div className="space-y-4">
              {segments.map((segment, index) => {
                const status = generationStatus[index];
                return (
                  <Card key={index}>
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-3">
                            <Badge variant="secondary" className="text-xs">
                              段落 {index + 1}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {getRoleLabel(segment.role)}
                            </Badge>

                            {/* 状态指示器 */}
                            {status?.status === 'pending' && (
                              <Badge variant="secondary" className="gap-1">
                                <Clock className="w-3 h-3" />
                                等待中
                              </Badge>
                            )}
                            {status?.status === 'generating' && (
                              <Badge variant="secondary" className="gap-1">
                                <Loader2 className="w-3 h-3 animate-spin" />
                                生成中
                              </Badge>
                            )}
                            {status?.status === 'completed' && (
                              <Badge variant="default" className="gap-1 bg-green-600">
                                <CheckCircle className="w-3 h-3" />
                                已完成
                              </Badge>
                            )}
                            {status?.status === 'error' && (
                              <Badge variant="destructive" className="gap-1">
                                <AlertCircle className="w-3 h-3" />
                                失败
                              </Badge>
                            )}
                          </div>

                          <p className="text-sm leading-relaxed mb-4">
                            {segment.text}
                          </p>

                          {status?.error && (
                            <div className="text-destructive text-sm mb-3 p-3 bg-destructive/10 rounded-md border border-destructive/20">
                              <strong>错误：</strong>{status.error}
                            </div>
                          )}

                          {status?.audioUrl && (
                            <div className="mt-4">
                              <audio controls className="w-full">
                                <source src={status.audioUrl} type="audio/mpeg" />
                                您的浏览器不支持音频播放。
                              </audio>
                            </div>
                          )}
                        </div>

                        {status?.status === 'error' && (
                          <Button
                            onClick={() => retryGeneration(index)}
                            disabled={isGenerating}
                            variant="outline"
                            size="sm"
                            className="gap-2"
                          >
                            <RotateCcw className="w-3 h-3" />
                            重试
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* 底部导航 */}
            {completedCount > 0 && (
              <Card className="mt-8">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold mb-1">音频生成完成！</h3>
                      <p className="text-sm text-muted-foreground">
                        已生成 {completedCount} 个音频片段，可以开始预览了
                      </p>
                    </div>
                    <Link href="/preview">
                      <Button size="lg" className="gap-2">
                        下一步：播放预览
                        <ArrowRight className="w-4 h-4" />
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            )}
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { TextSegment, AudioGenerationStatus, VOICE_ROLES } from '@/types';
import { loadSegmentsFromStorage } from '@/utils/textUtils';

export default function GeneratePage() {
  const [segments, setSegments] = useState<TextSegment[]>([]);
  const [generationStatus, setGenerationStatus] = useState<AudioGenerationStatus[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(-1);
  const [error, setError] = useState('');

  // 页面加载时读取数据
  useEffect(() => {
    console.log('正在加载保存的数据...'); // 调试信息
    const savedSegments = loadSegmentsFromStorage();
    console.log('读取到的数据:', savedSegments); // 调试信息

    if (savedSegments.length === 0) {
      console.log('没有找到保存的数据'); // 调试信息
      setError('没有找到文本数据，请先完成文本标注');
      return;
    }

    console.log('成功加载', savedSegments.length, '个文本段落'); // 调试信息
    setSegments(savedSegments);
    // 初始化生成状态
    const initialStatus: AudioGenerationStatus[] = savedSegments.map((_, index) => ({
      index,
      status: 'pending'
    }));
    setGenerationStatus(initialStatus);
  }, []);

  // 生成单个音频
  const generateSingleAudio = async (segment: TextSegment, index: number): Promise<boolean> => {
    try {
      setCurrentIndex(index);
      
      // 更新状态为生成中
      setGenerationStatus(prev => prev.map(status => 
        status.index === index 
          ? { ...status, status: 'generating' }
          : status
      ));

      const response = await fetch('/api/generate-audio', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: segment.text,
          voice: segment.role,
          index: index
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '生成失败');
      }

      // 更新状态为完成
      setGenerationStatus(prev => prev.map(status => 
        status.index === index 
          ? { 
              ...status, 
              status: 'completed',
              audioUrl: data.audioUrl
            }
          : status
      ));

      return true;
    } catch (error) {
      console.error(`生成音频 ${index} 失败:`, error);
      
      // 更新状态为错误
      setGenerationStatus(prev => prev.map(status => 
        status.index === index 
          ? { 
              ...status, 
              status: 'error',
              error: (error as Error).message
            }
          : status
      ));

      return false;
    }
  };

  // 开始生成所有音频
  const startGeneration = async () => {
    if (segments.length === 0) {
      setError('没有可生成的文本段落');
      return;
    }

    setIsGenerating(true);
    setError('');

    try {
      for (let i = 0; i < segments.length; i++) {
        const success = await generateSingleAudio(segments[i], i);
        if (!success) {
          // 如果某个音频生成失败，询问是否继续
          const shouldContinue = confirm(`第 ${i + 1} 段音频生成失败，是否继续生成剩余音频？`);
          if (!shouldContinue) {
            break;
          }
        }
        
        // 添加延迟避免API限制
        if (i < segments.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    } catch (error) {
      setError('生成过程中发生错误：' + (error as Error).message);
    } finally {
      setIsGenerating(false);
      setCurrentIndex(-1);
    }
  };

  // 重新生成单个音频
  const retryGeneration = async (index: number) => {
    if (isGenerating) return;
    
    setIsGenerating(true);
    await generateSingleAudio(segments[index], index);
    setIsGenerating(false);
  };

  // 获取角色显示名称
  const getRoleLabel = (role: string) => {
    const roleInfo = VOICE_ROLES.find(r => r.value === role);
    return roleInfo ? roleInfo.label : role;
  };

  // 计算进度
  const completedCount = generationStatus.filter(s => s.status === 'completed').length;
  const progress = segments.length > 0 ? (completedCount / segments.length) * 100 : 0;

  if (segments.length === 0 && !error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* 头部导航 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link 
              href="/annotate" 
              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            >
              ← 返回标注
            </Link>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              音频生成
            </h1>
          </div>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            {error}
            {error.includes('没有找到文本数据') && (
              <div className="mt-2">
                <Link 
                  href="/annotate" 
                  className="text-red-800 underline hover:text-red-900"
                >
                  前往文本标注页面
                </Link>
              </div>
            )}
          </div>
        )}

        {segments.length > 0 && (
          <>
            {/* 进度概览 */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  生成进度
                </h2>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {completedCount} / {segments.length} 完成
                </div>
              </div>
              
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-4">
                <div 
                  className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>

              <div className="flex justify-center">
                {!isGenerating ? (
                  <button
                    onClick={startGeneration}
                    className="px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
                  >
                    开始生成音频
                  </button>
                ) : (
                  <div className="flex items-center space-x-3">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
                    <span className="text-gray-600 dark:text-gray-400">
                      正在生成第 {currentIndex + 1} 段音频...
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* 段落列表 */}
            <div className="space-y-4">
              {segments.map((segment, index) => {
                const status = generationStatus[index];
                return (
                  <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-3">
                          <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            段落 {index + 1}
                          </span>
                          <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-xs">
                            {getRoleLabel(segment.role)}
                          </span>
                          
                          {/* 状态指示器 */}
                          <div className="flex items-center space-x-2">
                            {status?.status === 'pending' && (
                              <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded text-xs">
                                等待中
                              </span>
                            )}
                            {status?.status === 'generating' && (
                              <div className="flex items-center space-x-1">
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                                <span className="px-2 py-1 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded text-xs">
                                  生成中
                                </span>
                              </div>
                            )}
                            {status?.status === 'completed' && (
                              <span className="px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded text-xs">
                                已完成
                              </span>
                            )}
                            {status?.status === 'error' && (
                              <span className="px-2 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded text-xs">
                                失败
                              </span>
                            )}
                          </div>
                        </div>
                        
                        <p className="text-gray-900 dark:text-white text-sm leading-relaxed mb-3">
                          {segment.text}
                        </p>

                        {status?.error && (
                          <div className="text-red-600 dark:text-red-400 text-sm mb-3">
                            错误：{status.error}
                          </div>
                        )}

                        {status?.audioUrl && (
                          <audio controls className="w-full">
                            <source src={status.audioUrl} type="audio/mpeg" />
                            您的浏览器不支持音频播放。
                          </audio>
                        )}
                      </div>

                      {status?.status === 'error' && (
                        <button
                          onClick={() => retryGeneration(index)}
                          disabled={isGenerating}
                          className="ml-4 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50 transition-colors"
                        >
                          重试
                        </button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>

            {/* 底部导航 */}
            {completedCount > 0 && (
              <div className="mt-8 text-center">
                <Link
                  href="/preview"
                  className="inline-flex items-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  下一步：播放预览
                  <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}

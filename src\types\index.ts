// 角色类型定义
export type VoiceRole = 'Cherry' | '<PERSON>' | '<PERSON>' | 'Ch<PERSON>ie' | '<PERSON>' | 'J<PERSON>' | 'Sunny';

// 文本段落接口
export interface TextSegment {
  role: VoiceRole;
  text: string;
}

// 音频生成状态
export interface AudioGenerationStatus {
  index: number;
  status: 'pending' | 'generating' | 'completed' | 'error';
  audioUrl?: string;
  error?: string;
}

// DashScope API 请求接口
export interface DashScopeRequest {
  model: string;
  input: {
    text: string;
    voice: string;
  };
}

// DashScope API 响应接口
export interface DashScopeResponse {
  output: {
    audio: {
      url: string;
    };
  };
  request_id: string;
}

// 角色选项
export const VOICE_ROLES: { value: VoiceRole; label: string }[] = [
  { value: 'Cherry', label: 'Cherry' },
  { value: '<PERSON>', label: '<PERSON>' },
  { value: '<PERSON>', label: '<PERSON>' },
  { value: 'Ch<PERSON><PERSON>', label: '<PERSON><PERSON><PERSON>' },
  { value: '<PERSON>', label: '<PERSON>' },
  { value: 'J<PERSON>', label: '<PERSON><PERSON>' },
  { value: '<PERSON>', label: '<PERSON>' },
];

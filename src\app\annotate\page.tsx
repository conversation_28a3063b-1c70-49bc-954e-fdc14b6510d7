'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { TextSegment, VoiceRole, VOICE_ROLES } from '@/types';
import { segmentText, saveSegmentsToStorage, loadSegmentsFromStorage } from '@/utils/textUtils';

export default function AnnotatePage() {
  const [inputText, setInputText] = useState('');
  const [segments, setSegments] = useState<TextSegment[]>([]);
  const [isLoading, setSaveLoading] = useState(false);
  const [saveMessage, setSaveMessage] = useState('');

  // 页面加载时从本地存储读取数据
  useEffect(() => {
    const savedSegments = loadSegmentsFromStorage();
    if (savedSegments.length > 0) {
      setSegments(savedSegments);
      // 重新组合文本显示在输入框中
      const combinedText = savedSegments.map(seg => seg.text).join('\n');
      setInputText(combinedText);
    }
  }, []);

  // 处理文本分段
  const handleSegmentText = () => {
    if (!inputText.trim()) {
      alert('请输入文本内容');
      return;
    }

    const textSegments = segmentText(inputText);
    const newSegments: TextSegment[] = textSegments.map(text => ({
      role: 'Cherry' as VoiceRole,
      text: text
    }));

    setSegments(newSegments);

    // 自动保存分段结果
    try {
      saveSegmentsToStorage(newSegments);
      console.log('分段后自动保存成功'); // 调试信息
    } catch (error) {
      console.error('分段后自动保存失败:', error);
    }
  };

  // 更新段落角色
  const updateSegmentRole = (index: number, role: VoiceRole) => {
    const updatedSegments = [...segments];
    updatedSegments[index].role = role;
    setSegments(updatedSegments);

    // 自动保存
    try {
      saveSegmentsToStorage(updatedSegments);
      console.log('角色更新后自动保存成功'); // 调试信息
    } catch (error) {
      console.error('自动保存失败:', error);
    }
  };

  // 删除段落
  const removeSegment = (index: number) => {
    const updatedSegments = segments.filter((_, i) => i !== index);
    setSegments(updatedSegments);
  };

  // 保存到本地存储
  const handleSave = async () => {
    if (segments.length === 0) {
      alert('请先分段文本');
      return;
    }

    setSaveLoading(true);
    setSaveMessage('');

    try {
      console.log('正在保存数据:', segments); // 调试信息
      saveSegmentsToStorage(segments);

      // 验证保存是否成功
      const savedData = loadSegmentsFromStorage();
      console.log('保存后读取的数据:', savedData); // 调试信息

      if (savedData.length === segments.length) {
        setSaveMessage('保存成功！');
      } else {
        setSaveMessage('保存可能不完整，请重试');
      }
      setTimeout(() => setSaveMessage(''), 3000);
    } catch (error) {
      console.error('保存错误:', error); // 调试信息
      setSaveMessage('保存失败：' + (error as Error).message);
    } finally {
      setSaveLoading(false);
    }
  };

  // 清空所有数据
  const handleClear = () => {
    if (confirm('确定要清空所有数据吗？')) {
      setInputText('');
      setSegments([]);
      localStorage.removeItem('podcast_segments');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* 头部导航 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link 
              href="/" 
              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            >
              ← 返回首页
            </Link>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              文本标注
            </h1>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={handleClear}
              className="px-4 py-2 text-red-600 border border-red-600 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
            >
              清空
            </button>
          </div>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* 左侧：文本输入区域 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              输入文本
            </h2>
            <textarea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="请输入要转换为播客的文本内容..."
              className="w-full h-64 p-4 border border-gray-300 dark:border-gray-600 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
            <div className="mt-4 flex space-x-4">
              <button
                onClick={handleSegmentText}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                自动分段
              </button>
              <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                文本将按换行符或句号自动分段
              </div>
            </div>
          </div>

          {/* 右侧：分段结果和角色选择 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                分段结果 ({segments.length})
              </h2>
              {segments.length > 0 && (
                <button
                  onClick={handleSave}
                  disabled={isLoading}
                  className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors"
                >
                  {isLoading ? '保存中...' : '保存'}
                </button>
              )}
            </div>

            {saveMessage && (
              <div className={`mb-4 p-3 rounded-lg ${
                saveMessage.includes('成功') 
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' 
                  : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
              }`}>
                {saveMessage}
              </div>
            )}

            <div className="space-y-4 max-h-96 overflow-y-auto">
              {segments.length === 0 ? (
                <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                  请先在左侧输入文本并点击"自动分段"
                </div>
              ) : (
                segments.map((segment, index) => (
                  <div key={index} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        段落 {index + 1}
                      </span>
                      <button
                        onClick={() => removeSegment(index)}
                        className="text-red-500 hover:text-red-700 text-sm"
                      >
                        删除
                      </button>
                    </div>
                    <p className="text-gray-900 dark:text-white mb-3 text-sm leading-relaxed">
                      {segment.text}
                    </p>
                    <div className="flex items-center space-x-2">
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        角色：
                      </label>
                      <select
                        value={segment.role}
                        onChange={(e) => updateSegmentRole(index, e.target.value as VoiceRole)}
                        className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                      >
                        {VOICE_ROLES.map(role => (
                          <option key={role.value} value={role.value}>
                            {role.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        {/* 底部导航 */}
        {segments.length > 0 && (
          <div className="mt-8 text-center">
            <Link
              href="/generate"
              className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              下一步：生成音频
              <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}

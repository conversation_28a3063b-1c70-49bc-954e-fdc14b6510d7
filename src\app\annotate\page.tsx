'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { TextSegment, VoiceRole, VOICE_ROLES } from '@/types';
import { segmentText, saveSegmentsToStorage, loadSegmentsFromStorage } from '@/utils/textUtils';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, ArrowRight, FileText, Trash2, Save, Zap, Users } from "lucide-react";

export default function AnnotatePage() {
  const [inputText, setInputText] = useState('');
  const [segments, setSegments] = useState<TextSegment[]>([]);
  const [isLoading, setSaveLoading] = useState(false);
  const [saveMessage, setSaveMessage] = useState('');

  // 页面加载时从本地存储读取数据
  useEffect(() => {
    const savedSegments = loadSegmentsFromStorage();
    if (savedSegments.length > 0) {
      setSegments(savedSegments);
      // 重新组合文本显示在输入框中
      const combinedText = savedSegments.map(seg => seg.text).join('\n');
      setInputText(combinedText);
    }
  }, []);

  // 处理文本分段
  const handleSegmentText = () => {
    if (!inputText.trim()) {
      alert('请输入文本内容');
      return;
    }

    const textSegments = segmentText(inputText);
    const newSegments: TextSegment[] = textSegments.map(text => ({
      role: 'Cherry' as VoiceRole,
      text: text
    }));

    setSegments(newSegments);

    // 自动保存分段结果
    try {
      saveSegmentsToStorage(newSegments);
      console.log('分段后自动保存成功'); // 调试信息
    } catch (error) {
      console.error('分段后自动保存失败:', error);
    }
  };

  // 更新段落角色
  const updateSegmentRole = (index: number, role: VoiceRole) => {
    const updatedSegments = [...segments];
    updatedSegments[index].role = role;
    setSegments(updatedSegments);

    // 自动保存
    try {
      saveSegmentsToStorage(updatedSegments);
      console.log('角色更新后自动保存成功'); // 调试信息
    } catch (error) {
      console.error('自动保存失败:', error);
    }
  };

  // 删除段落
  const removeSegment = (index: number) => {
    const updatedSegments = segments.filter((_, i) => i !== index);
    setSegments(updatedSegments);
  };

  // 保存到本地存储
  const handleSave = async () => {
    if (segments.length === 0) {
      alert('请先分段文本');
      return;
    }

    setSaveLoading(true);
    setSaveMessage('');

    try {
      console.log('正在保存数据:', segments); // 调试信息
      saveSegmentsToStorage(segments);

      // 验证保存是否成功
      const savedData = loadSegmentsFromStorage();
      console.log('保存后读取的数据:', savedData); // 调试信息

      if (savedData.length === segments.length) {
        setSaveMessage('保存成功！');
      } else {
        setSaveMessage('保存可能不完整，请重试');
      }
      setTimeout(() => setSaveMessage(''), 3000);
    } catch (error) {
      console.error('保存错误:', error); // 调试信息
      setSaveMessage('保存失败：' + (error as Error).message);
    } finally {
      setSaveLoading(false);
    }
  };

  // 清空所有数据
  const handleClear = () => {
    if (confirm('确定要清空所有数据吗？')) {
      setInputText('');
      setSegments([]);
      localStorage.removeItem('podcast_segments');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted/20 to-background">
      <div className="container mx-auto px-4 py-8">
        {/* 头部导航 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link href="/">
              <Button variant="ghost" size="sm" className="gap-2">
                <ArrowLeft className="w-4 h-4" />
                返回首页
              </Button>
            </Link>
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">文本标注</h1>
                <p className="text-muted-foreground">智能分段并分配角色</p>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="gap-2">
              <Users className="w-3 h-3" />
              {segments.length} 个段落
            </Badge>
            <Button
              onClick={handleClear}
              variant="outline"
              size="sm"
              className="gap-2 text-destructive hover:text-destructive"
            >
              <Trash2 className="w-4 h-4" />
              清空
            </Button>
          </div>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* 左侧：文本输入区域 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                输入文本
              </CardTitle>
              <CardDescription>
                输入要转换为播客的文本内容，系统将自动进行智能分段
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Textarea
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                placeholder="请输入要转换为播客的文本内容..."
                className="min-h-[300px] resize-none"
              />
              <div className="flex items-center justify-between">
                <Button
                  onClick={handleSegmentText}
                  className="gap-2"
                  disabled={!inputText.trim()}
                >
                  <Zap className="w-4 h-4" />
                  自动分段
                </Button>
                <div className="text-sm text-muted-foreground">
                  文本将按换行符或句号自动分段
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 右侧：分段结果和角色选择 */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    分段结果
                  </CardTitle>
                  <CardDescription>
                    为每个段落分配合适的角色
                  </CardDescription>
                </div>
                {segments.length > 0 && (
                  <Button
                    onClick={handleSave}
                    disabled={isLoading}
                    variant="outline"
                    size="sm"
                    className="gap-2"
                  >
                    <Save className="w-4 h-4" />
                    {isLoading ? '保存中...' : '保存'}
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {saveMessage && (
                <div className={`mb-4 p-3 rounded-lg border ${
                  saveMessage.includes('成功')
                    ? 'bg-green-50 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'
                    : 'bg-red-50 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800'
                }`}>
                  {saveMessage}
                </div>
              )}

              <div className="space-y-4 max-h-96 overflow-y-auto">
                {segments.length === 0 ? (
                  <div className="text-center text-muted-foreground py-12">
                    <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>请先在左侧输入文本并点击"自动分段"</p>
                  </div>
                ) : (
                  segments.map((segment, index) => (
                    <Card key={index} className="relative">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-3">
                          <Badge variant="secondary" className="text-xs">
                            段落 {index + 1}
                          </Badge>
                          <Button
                            onClick={() => removeSegment(index)}
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                        <p className="text-sm leading-relaxed mb-4 text-foreground">
                          {segment.text}
                        </p>
                        <div className="flex items-center space-x-2">
                          <label className="text-sm font-medium text-muted-foreground">
                            角色：
                          </label>
                          <select
                            value={segment.role}
                            onChange={(e) => updateSegmentRole(index, e.target.value as VoiceRole)}
                            className="px-3 py-1 border border-input rounded-md text-sm focus:ring-2 focus:ring-ring focus:border-transparent bg-background"
                          >
                            {VOICE_ROLES.map(role => (
                              <option key={role.value} value={role.value}>
                                {role.label}
                              </option>
                            ))}
                          </select>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 底部导航 */}
        {segments.length > 0 && (
          <Card className="mt-8">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold mb-1">准备就绪！</h3>
                  <p className="text-sm text-muted-foreground">
                    已完成 {segments.length} 个段落的标注，可以开始生成音频了
                  </p>
                </div>
                <Link href="/generate">
                  <Button size="lg" className="gap-2">
                    下一步：生成音频
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}

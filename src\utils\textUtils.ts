import { TextSegment, VoiceRole } from '@/types';

/**
 * 自动分段函数：将长文本按段落分割
 * @param text 输入的长文本
 * @returns 分段后的文本数组
 */
export function segmentText(text: string): string[] {
  if (!text.trim()) return [];
  
  // 按换行符分割
  let segments = text.split('\n').filter(segment => segment.trim() !== '');
  
  // 如果没有换行符，按句号分割
  if (segments.length === 1) {
    segments = text.split('。').filter(segment => segment.trim() !== '');
    // 为每个段落重新添加句号（除了最后一个）
    segments = segments.map((segment, index) => {
      const trimmed = segment.trim();
      if (index < segments.length - 1 && trimmed && !trimmed.endsWith('。')) {
        return trimmed + '。';
      }
      return trimmed;
    });
  }
  
  return segments.filter(segment => segment.trim() !== '');
}

/**
 * 保存文本段落到本地存储
 * @param segments 文本段落数组
 */
export function saveSegmentsToStorage(segments: TextSegment[]): void {
  try {
    localStorage.setItem('podcast_segments', JSON.stringify(segments));
  } catch (error) {
    console.error('保存到本地存储失败:', error);
    throw new Error('保存失败，请检查浏览器存储权限');
  }
}

/**
 * 从本地存储读取文本段落
 * @returns 文本段落数组
 */
export function loadSegmentsFromStorage(): TextSegment[] {
  try {
    const stored = localStorage.getItem('podcast_segments');
    if (!stored) return [];
    return JSON.parse(stored) as TextSegment[];
  } catch (error) {
    console.error('从本地存储读取失败:', error);
    return [];
  }
}

/**
 * 清除本地存储的数据
 */
export function clearStoredSegments(): void {
  try {
    localStorage.removeItem('podcast_segments');
  } catch (error) {
    console.error('清除本地存储失败:', error);
  }
}
